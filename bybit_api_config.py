import os
import logging
from typing import Dict, Optional, List, Any

logger = logging.getLogger(__name__)

class BybitAPIConfig:
    """Production-Optimized Bybit API configuration for sub-100ms latency"""

    def __init__(self):
        self.testnet = False  # PRODUCTION ONLY - NO TESTNET ALLOWED
        self.api_key = self._get_api_key()
        self.api_secret = self._get_api_secret()
        self.endpoints = self._get_endpoints()
        self.performance_config = self._get_performance_config()
        
    def _get_api_key(self) -> str:
        """Get API key with proper validation"""
        api_key = os.getenv('BYBIT_API_KEY', '')
        if not api_key:
            logger.warning("BYBIT_API_KEY not found in environment")
            return ''
        return api_key
        
    def _get_api_secret(self) -> str:
        """Get API secret with proper validation"""
        api_secret = os.getenv('BYBIT_SECRET', '')
        if not api_secret:
            logger.warning("BYBIT_SECRET not found in environment")
            return ''
        return api_secret
        
    def _get_endpoints(self) -> Dict[str, str]:
        """Get correct API endpoints"""
        if self.testnet:
            # This block should not be reached due to self.testnet = False
            logger.warning("Attempting to use testnet, but live trading is forced.")
            return {
                'rest': 'https://api.bybit.com',
                'websocket_public': 'wss://stream.bybit.com/v5/public/spot',
                'websocket_private': 'wss://stream.bybit.com/v5/private'
            }
        else:
            return {
                'rest': 'https://api.bybit.com',
                'websocket_public': 'wss://stream.bybit.com/v5/public/spot',
                'websocket_private': 'wss://stream.bybit.com/v5/private'
            }

    def _get_performance_config(self) -> Dict[str, Any]:
        """Get performance optimization configuration for sub-100ms latency"""
        return {
            'max_connections': 1000,
            'max_connections_per_host': 200,
            'connection_timeout': 1,
            'read_timeout': 2,
            'total_timeout': 5,
            'keepalive_timeout': 120,
            'dns_cache_ttl': 1200,
            'rate_limit_per_second': 120,
            'burst_allowance': 200,
            'tcp_nodelay': True,
            'happy_eyeballs_delay': 0.1
        }
    
    def get_valid_symbols(self) -> List[str]:
        """Get list of valid trading symbols"""
        # Use the most common and stable symbols
        return [
            'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'SOLUSDT',
            'XRPUSDT', 'DOTUSDT', 'DOGEUSDT', 'AVAXUSDT', 'MATICUSDT'
        ]
    
    def is_valid_symbol(self, symbol: str) -> bool:
        """Check if symbol is valid"""
        return symbol in self.get_valid_symbols()
    
    def validate_credentials(self) -> bool:
        """Validate API credentials format"""
        if not self.api_key or not self.api_secret:
            return False
        
        # Basic format validation
        if len(self.api_key) < 10 or len(self.api_secret) < 10:
            return False
            
        return True

# Global configuration instance
bybit_config = BybitAPIConfig()

def get_bybit_config():
    """Get the global Bybit configuration"""
    return bybit_config

def safe_symbol_request(symbol: str) -> str:
    """Ensure symbol is in correct format for Bybit"""
    # Remove any extra characters and ensure proper format
    symbol = symbol.upper().strip()
    
    # Validate against known symbols
    if bybit_config.is_valid_symbol(symbol):
        return symbol
    
    # Try to correct common variations
    if symbol.endswith('USD'):
        symbol += 'T'
    
    if bybit_config.is_valid_symbol(symbol):
        return symbol
        
    # Fallback to BTCUSDT if symbol is invalid
    logger.warning(f"Invalid symbol {symbol}, falling back to BTCUSDT")
    return 'BTCUSDT'

def safe_websocket_connection(endpoint_type: str = 'public') -> Optional[str]:
    """Get safe WebSocket endpoint"""
    try:
        endpoints = bybit_config.endpoints
        if endpoint_type == 'public':
            return endpoints['websocket_public']
        elif endpoint_type == 'private':
            return endpoints['websocket_private']
        else:
            return endpoints['websocket_public']
    except Exception as e:
        logger.error(f"WebSocket endpoint error: {e}")
        return None
