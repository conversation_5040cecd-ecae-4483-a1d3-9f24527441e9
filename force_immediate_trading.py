#!/usr/bin/env python3
"""
FORCE IMMEDIATE TRADING - REAL PROFIT GENERATION
Direct trading execution without web interface
"""

import asyncio
import logging
import sys
# Ensure sys.warnoptions exists
if not hasattr(sys, "warnoptions"): sys.warnoptions = []
import os
from pathlib import Path

# Add the bot directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

# Set environment variables for live trading
os.environ['BYBIT_API_KEY'] = 'WbQDRvmEzuiNyDI0QyxN3nXQEj'
os.environ['BYBIT_API_SECRET'] = 'vdviKwMlqfCUy9AJ7V0hVLKXMGaWgMYyFUga'
os.environ['BYBIT_LIVE_TRADING'] = 'true'
os.environ['FORCE_LIVE_TRADING'] = 'true'
os.environ['IMMEDIATE_TRADING'] = 'true'

from bybit_bot.core.config import BotConfig
from bybit_bot.exchange.enhanced_bybit_client import EnhancedBybitClient
from bybit_bot.database.connection import DatabaseManager

class ImmediateProfitTrader:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.config = None
        self.client = None
        self.db_manager = None
        
    async def initialize(self):
        """Initialize trading components"""
        try:
            print("=" * 70)
            print("🚀 IMMEDIATE PROFIT GENERATION - LIVE TRADING")
            print("=" * 70)
            
            # Load configuration
            self.config = BotConfig()
            print("[OK] Configuration loaded")
            
            # Initialize database
            self.db_manager = DatabaseManager(self.config)
            await self.db_manager.initialize()
            print("[OK] Database connected")
            
            # Initialize Bybit client
            self.client = EnhancedBybitClient(self.config, self.db_manager)
            await self.client.initialize()
            print("[OK] Bybit client initialized")
            
            return True
            
        except Exception as e:
            print(f"[ERROR] Initialization failed: {e}")
            return False
    
    async def check_account_and_close_positions(self):
        """Check account and close any blocking positions"""
        try:
            print("\n📊 ACCOUNT STATUS CHECK")
            print("-" * 40)
            
            # Get account info
            account_info = await self.client.get_account_info()
            print(f"Account Type: {account_info.get('accountType', 'Unknown')}")
            
            # Get wallet balance
            balance_info = await self.client.get_wallet_balance()
            total_equity = float(balance_info.get('totalEquity', 0))
            available_balance = float(balance_info.get('availableBalance', 0))
            
            print(f"Total Equity: ${total_equity:.2f}")
            print(f"Available Balance: ${available_balance:.2f}")
            
            # Get positions
            positions = await self.client.get_positions()
            open_positions = [pos for pos in positions if float(pos.get('size', 0)) != 0]
            
            print(f"Open Positions: {len(open_positions)}")
            
            # Close all positions to free funds
            if open_positions:
                print("\n🔄 CLOSING POSITIONS TO FREE FUNDS")
                for pos in open_positions:
                    try:
                        symbol = pos['symbol']
                        size = abs(float(pos['size']))
                        side = "Sell" if pos['side'] == "Buy" else "Buy"
                        
                        print(f"  Closing {symbol}: {pos['size']} {pos['side']} position...")
                        
                        result = await self.client.place_order(
                            symbol=symbol,
                            side=side,
                            order_type="Market",
                            qty=size,
                            reduce_only=True
                        )
                        
                        if result and result.get('orderId'):
                            print(f"  ✅ {symbol} position closed - Order: {result['orderId']}")
                        else:
                            print(f"  ❌ Failed to close {symbol}")
                            
                    except Exception as e:
                        print(f"  ❌ Error closing {pos['symbol']}: {e}")
                
                # Wait for positions to close
                await asyncio.sleep(3)
                
                # Re-check balance
                balance_info = await self.client.get_wallet_balance()
                available_balance = float(balance_info.get('availableBalance', 0))
                print(f"\n💰 Updated Available Balance: ${available_balance:.2f}")
            
            return available_balance
            
        except Exception as e:
            print(f"[ERROR] Account check failed: {e}")
            return 0
    
    async def execute_immediate_trades(self, available_balance):
        """Execute immediate profitable trades"""
        try:
            print("\n🎯 IMMEDIATE TRADE EXECUTION")
            print("-" * 40)
            
            if available_balance < 10:
                print(f"❌ Insufficient funds: ${available_balance:.2f}")
                return False
            
            # Trading pairs
            symbols = ['BTCUSDT', 'ETHUSDT']
            
            # Position size per trade (10% of available balance)
            position_size = available_balance * 0.1
            print(f"Position Size per Trade: ${position_size:.2f}")
            
            trades_executed = 0
            
            for symbol in symbols:
                try:
                    print(f"\n📈 Trading {symbol}...")
                    
                    # Get current market data
                    ticker = await self.client.get_ticker(symbol)
                    current_price = float(ticker.get('lastPrice', 0))
                    
                    if current_price <= 0:
                        print(f"  ❌ Invalid price for {symbol}")
                        continue
                    
                    # Calculate quantity
                    qty = position_size / current_price
                    min_qty = 0.001 if 'BTC' in symbol else 0.01
                    
                    if qty < min_qty:
                        print(f"  ❌ Quantity too small: {qty:.6f} < {min_qty}")
                        continue
                    
                    print(f"  Current Price: ${current_price:.2f}")
                    print(f"  Trade Quantity: {qty:.6f}")
                    
                    # Execute Buy order (scalping strategy)
                    buy_result = await self.client.place_order(
                        symbol=symbol,
                        side="Buy",
                        order_type="Market",
                        qty=qty
                    )
                    
                    if buy_result and buy_result.get('orderId'):
                        print(f"  ✅ BUY ORDER EXECUTED - ID: {buy_result['orderId']}")
                        trades_executed += 1
                        
                        # Wait a moment
                        await asyncio.sleep(1)
                        
                        # Set take profit (0.5% above buy price)
                        take_profit_price = current_price * 1.005
                        
                        sell_result = await self.client.place_order(
                            symbol=symbol,
                            side="Sell",
                            order_type="Limit",
                            qty=qty,
                            price=take_profit_price
                        )
                        
                        if sell_result and sell_result.get('orderId'):
                            print(f"  ✅ TAKE PROFIT SET - ID: {sell_result['orderId']} @ ${take_profit_price:.2f}")
                        else:
                            print(f"  ⚠️ Take profit failed, manual exit may be needed")
                    
                    else:
                        print(f"  ❌ Buy order failed for {symbol}")
                
                except Exception as e:
                    print(f"  ❌ Error trading {symbol}: {e}")
                
                # Small delay between symbols
                await asyncio.sleep(0.5)
            
            print(f"\n🎉 TRADES COMPLETED: {trades_executed} orders executed")
            return trades_executed > 0
            
        except Exception as e:
            print(f"[ERROR] Trade execution failed: {e}")
            return False
    
    async def monitor_positions(self):
        """Monitor and manage active positions"""
        try:
            print("\n👀 MONITORING ACTIVE POSITIONS")
            print("-" * 40)
            
            for _ in range(10):  # Monitor for 10 cycles
                positions = await self.client.get_positions()
                open_positions = [pos for pos in positions if float(pos.get('size', 0)) != 0]
                
                if open_positions:
                    print(f"\n📊 Active Positions: {len(open_positions)}")
                    for pos in open_positions:
                        pnl = float(pos.get('unrealisedPnl', 0))
                        pnl_pct = float(pos.get('unrealisedPnlPct', 0)) * 100
                        print(f"  {pos['symbol']}: {pos['side']} {pos['size']} | PnL: ${pnl:.2f} ({pnl_pct:.2f}%)")
                else:
                    print("✅ No open positions")
                
                # Check orders
                for symbol in ['BTCUSDT', 'ETHUSDT']:
                    try:
                        orders = await self.client.get_open_orders(symbol)
                        if orders:
                            print(f"  📋 {symbol} orders: {len(orders)}")
                    except:
                        pass
                
                await asyncio.sleep(30)  # Check every 30 seconds
                
        except Exception as e:
            print(f"[ERROR] Monitoring failed: {e}")
    
    async def run(self):
        """Main execution"""
        try:
            # Initialize
            if not await self.initialize():
                return False
            
            # Check account and close blocking positions
            available_balance = await self.check_account_and_close_positions()
            
            if available_balance < 10:
                print("\n❌ INSUFFICIENT FUNDS FOR TRADING")
                return False
            
            # Execute immediate trades
            if await self.execute_immediate_trades(available_balance):
                print("\n✅ PROFIT GENERATION STARTED!")
                
                # Monitor positions
                await self.monitor_positions()
            else:
                print("\n❌ FAILED TO START TRADING")
                return False
            
            return True
            
        except Exception as e:
            print(f"[CRITICAL ERROR] {e}")
            return False

async def main():
    """Main function"""
    trader = ImmediateProfitTrader()
    
    print("🚀 STARTING IMMEDIATE PROFIT GENERATION")
    print("⚡ LIVE TRADING MODE - REAL MONEY")
    print("💰 TARGETING SCALPING PROFITS")
    print()
    
    success = await trader.run()
    
    if success:
        print("\n" + "=" * 70)
        print("🎯 PROFIT GENERATION SYSTEM ACTIVE!")
        print("💹 Real trades executed - monitoring for profits...")
        print("=" * 70)
    else:
        print("\n❌ Failed to activate profit generation")

if __name__ == "__main__":
    asyncio.run(main())
