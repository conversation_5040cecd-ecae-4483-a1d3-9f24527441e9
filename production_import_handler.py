#!/usr/bin/env python3
"""
Production Import Handler
Handles missing dependencies gracefully for production deployment
"""

import sys
import warnings

# Suppress all warnings for production
warnings.filterwarnings("ignore")

class ProductionImportHandler:
    """Handle missing imports gracefully in production"""
    
    def __init__(self):
        self.missing_packages = []
        self.available_packages = []
    
    def safe_import(self, package_name, module_name=None, fallback_class=None):
        """Safely import a package with fallback"""
        try:
            if module_name:
                module = __import__(package_name, fromlist=[module_name])
                return getattr(module, module_name)
            else:
                return __import__(package_name)
        except ImportError:
            self.missing_packages.append(package_name)
            print(f"[WARNING] {package_name} not available - using fallback")
            
            if fallback_class:
                return fallback_class
            else:
                return self._create_dummy_class(package_name)
    
    def _create_dummy_class(self, package_name):
        """Create a dummy class for missing packages"""
        class DummyClass:
            def __init__(self, *args, **kwargs):
                pass
            
            def __call__(self, *args, **kwargs):
                return self
            
            def __getattr__(self, name):
                return self
            
            def __setattr__(self, name, value):
                pass
            
            def __bool__(self):
                return False
        
        return DummyClass
    
    def get_status_report(self):
        """Get import status report"""
        return {
            'missing_packages': self.missing_packages,
            'available_packages': self.available_packages,
            'total_missing': len(self.missing_packages)
        }

# Global handler instance
import_handler = ProductionImportHandler()

# Handle common missing packages
try:
    import tweepy
    TWEEPY_AVAILABLE = True
except ImportError:
    print("[WARNING] tweepy not available - social media analysis disabled")
    TWEEPY_AVAILABLE = False
    tweepy = import_handler.safe_import('tweepy')

try:
    import praw
    PRAW_AVAILABLE = True
except ImportError:
    print("[WARNING] praw not available - Reddit analysis disabled")
    PRAW_AVAILABLE = False
    praw = import_handler.safe_import('praw')

try:
    import yfinance
    YFINANCE_AVAILABLE = True
except ImportError:
    print("[WARNING] yfinance not available - Yahoo Finance data disabled")
    YFINANCE_AVAILABLE = False
    yfinance = import_handler.safe_import('yfinance')

try:
    import ccxt
    CCXT_AVAILABLE = True
except ImportError:
    print("[WARNING] ccxt not available - multi-exchange support disabled")
    CCXT_AVAILABLE = False
    ccxt = import_handler.safe_import('ccxt')

try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    print("[WARNING] openai not available - GPT integration disabled")
    OPENAI_AVAILABLE = False
    openai = import_handler.safe_import('openai')

# Export commonly used imports
__all__ = [
    'import_handler',
    'tweepy', 'TWEEPY_AVAILABLE',
    'praw', 'PRAW_AVAILABLE', 
    'yfinance', 'YFINANCE_AVAILABLE',
    'ccxt', 'CCXT_AVAILABLE',
    'openai', 'OPENAI_AVAILABLE'
]

def get_production_status():
    """Get overall production import status"""
    status = import_handler.get_status_report()
    
    print("\n" + "="*60)
    print("PRODUCTION IMPORT STATUS")
    print("="*60)
    print(f"Missing packages: {status['total_missing']}")
    
    if status['missing_packages']:
        print("Missing:")
        for pkg in status['missing_packages']:
            print(f"  - {pkg}")
    
    print("="*60)
    return status

if __name__ == "__main__":
    get_production_status()
