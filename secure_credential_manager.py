#!/usr/bin/env python3
"""
Secure Credential Management System for Production Trading
Implements encrypted credential storage with automatic rotation
"""

import os
import json
import base64
import hashlib
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import P<PERSON><PERSON>DF2HMAC
from datetime import datetime, timedelta
from typing import Dict, Optional
import logging

class SecureCredentialManager:
    """Production-grade secure credential management"""
    
    def __init__(self, master_password: str = None):
        self.logger = logging.getLogger(__name__)
        self.credentials_file = "E:/bybit_bot_data/credentials.encrypted"
        self.salt_file = "E:/bybit_bot_data/credentials.salt"
        self.rotation_log = "E:/bybit_bot_logs/credential_rotation.log"
        
        # Initialize encryption
        self.master_password = master_password or os.getenv('MASTER_PASSWORD', 'default_secure_key')
        self.cipher_suite = self._initialize_encryption()
        
        # Credential rotation settings
        self.rotation_interval = timedelta(hours=24)  # Rotate every 24 hours
        self.last_rotation = None
        
    def _initialize_encryption(self) -> <PERSON><PERSON><PERSON>:
        """Initialize encryption cipher"""
        try:
            # Load or create salt
            if os.path.exists(self.salt_file):
                with open(self.salt_file, 'rb') as f:
                    salt = f.read()
            else:
                salt = os.urandom(16)
                os.makedirs(os.path.dirname(self.salt_file), exist_ok=True)
                with open(self.salt_file, 'wb') as f:
                    f.write(salt)
            
            # Derive key from password
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )
            key = base64.urlsafe_b64encode(kdf.derive(self.master_password.encode()))
            return Fernet(key)
            
        except Exception as e:
            self.logger.error(f"Failed to initialize encryption: {e}")
            raise
    
    def store_credentials(self, credentials: Dict[str, str]) -> bool:
        """Store encrypted credentials"""
        try:
            # Add metadata
            credential_data = {
                'credentials': credentials,
                'created_at': datetime.utcnow().isoformat(),
                'last_rotation': datetime.utcnow().isoformat(),
                'rotation_count': 0
            }
            
            # Encrypt and store
            encrypted_data = self.cipher_suite.encrypt(
                json.dumps(credential_data).encode()
            )
            
            os.makedirs(os.path.dirname(self.credentials_file), exist_ok=True)
            with open(self.credentials_file, 'wb') as f:
                f.write(encrypted_data)
            
            self.logger.info("Credentials stored securely")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to store credentials: {e}")
            return False
    
    def load_credentials(self) -> Optional[Dict[str, str]]:
        """Load and decrypt credentials"""
        try:
            if not os.path.exists(self.credentials_file):
                self.logger.warning("No credentials file found")
                return None
            
            with open(self.credentials_file, 'rb') as f:
                encrypted_data = f.read()
            
            decrypted_data = self.cipher_suite.decrypt(encrypted_data)
            credential_data = json.loads(decrypted_data.decode())
            
            # Check if rotation is needed
            last_rotation = datetime.fromisoformat(credential_data['last_rotation'])
            if datetime.utcnow() - last_rotation > self.rotation_interval:
                self.logger.warning("Credentials need rotation")
            
            return credential_data['credentials']
            
        except Exception as e:
            self.logger.error(f"Failed to load credentials: {e}")
            return None
    
    def get_secure_credential(self, key: str) -> str:
        """Get specific credential securely"""
        credentials = self.load_credentials()
        if credentials and key in credentials:
            return credentials[key]
        
        # Fallback to environment variable
        env_value = os.getenv(key, '')
        if env_value:
            self.logger.warning(f"Using environment variable for {key}")
            return env_value
        
        self.logger.error(f"Credential {key} not found")
        return ''
    
    def rotate_credentials(self, new_credentials: Dict[str, str]) -> bool:
        """Rotate credentials with logging"""
        try:
            # Load existing data
            existing_data = {}
            if os.path.exists(self.credentials_file):
                with open(self.credentials_file, 'rb') as f:
                    encrypted_data = f.read()
                decrypted_data = self.cipher_suite.decrypt(encrypted_data)
                existing_data = json.loads(decrypted_data.decode())
            
            # Update with new credentials
            credential_data = {
                'credentials': new_credentials,
                'created_at': existing_data.get('created_at', datetime.utcnow().isoformat()),
                'last_rotation': datetime.utcnow().isoformat(),
                'rotation_count': existing_data.get('rotation_count', 0) + 1
            }
            
            # Store updated credentials
            encrypted_data = self.cipher_suite.encrypt(
                json.dumps(credential_data).encode()
            )
            
            with open(self.credentials_file, 'wb') as f:
                f.write(encrypted_data)
            
            # Log rotation
            self._log_rotation(credential_data['rotation_count'])
            
            self.logger.info("Credentials rotated successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to rotate credentials: {e}")
            return False
    
    def _log_rotation(self, rotation_count: int):
        """Log credential rotation for audit"""
        try:
            os.makedirs(os.path.dirname(self.rotation_log), exist_ok=True)
            with open(self.rotation_log, 'a') as f:
                f.write(f"{datetime.utcnow().isoformat()}: Credential rotation #{rotation_count}\n")
        except Exception as e:
            self.logger.error(f"Failed to log rotation: {e}")
    
    def validate_credentials(self) -> bool:
        """Validate stored credentials"""
        try:
            credentials = self.load_credentials()
            if not credentials:
                return False
            
            required_keys = ['BYBIT_API_KEY', 'BYBIT_SECRET']
            for key in required_keys:
                if key not in credentials or not credentials[key]:
                    self.logger.error(f"Missing required credential: {key}")
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Credential validation failed: {e}")
            return False

# Global instance for easy access
_credential_manager = None

def get_secure_credential(key: str) -> str:
    """Global function to get secure credentials"""
    global _credential_manager
    if _credential_manager is None:
        _credential_manager = SecureCredentialManager()
    return _credential_manager.get_secure_credential(key)

def initialize_credentials(credentials: Dict[str, str]) -> bool:
    """Initialize secure credential storage"""
    global _credential_manager
    if _credential_manager is None:
        _credential_manager = SecureCredentialManager()
    return _credential_manager.store_credentials(credentials)

if __name__ == "__main__":
    # Example usage
    manager = SecureCredentialManager()
    
    # Store example credentials (replace with real ones)
    test_credentials = {
        'BYBIT_API_KEY': 'your_production_api_key',
        'BYBIT_SECRET': 'your_production_secret'
    }
    
    if manager.store_credentials(test_credentials):
        print("Credentials stored successfully")
        
        # Test retrieval
        api_key = manager.get_secure_credential('BYBIT_API_KEY')
        print(f"Retrieved API key: {api_key[:10]}...")
    else:
        print("Failed to store credentials")
