# Super GPT Bybit Bot - Enhanced Requirements
# Core trading and API dependencies
fastapi==0.104.1
uvicorn[standard]==0.24.0
aiohttp==3.9.1
websockets==12.0
pybit==5.7.0

# Database and async support
asyncpg==0.29.0
sqlalchemy==2.0.23
alembic==1.13.1

# Data processing and analysis
pandas==2.1.4
numpy==1.25.2
scikit-learn==1.3.2
scipy==1.11.4

# Machine Learning and AI
torch==2.1.2
transformers==4.36.2
datasets==2.16.1
huggingface-hub==0.20.2

# Technical analysis
ta-lib==0.4.28
ta==0.10.2
technical-indicators==0.1.1

# Time series analysis
statsmodels==0.14.1

# Web scraping and data collection
beautifulsoup4==4.12.2
lxml==4.9.4
selenium==4.16.0

# Configuration and environment
python-dotenv==1.0.0
pydantic==2.5.2
pydantic-settings==2.1.0

# Logging and monitoring
loguru==0.7.2
prometheus-client==0.19.0

# Async utilities
asyncio-throttle==1.0.2
aiodns==3.1.1

# Networking and HTTP
httpx==0.25.2
certifi==2023.11.17

# Data validation and parsing
marshmallow==3.20.1
jsonschema==4.20.0

# Crypto and security
cryptography==41.0.8
pyotp==2.9.0

# System monitoring
psutil==5.9.6
py-cpuinfo==9.0.0

# Financial calculations
empyrical==0.5.5
pyfolio==0.9.2

# Additional utilities
python-dateutil==2.8.2
pytz==2023.3
tzdata==2023.4
click==8.1.7
tqdm==4.66.1

# PRODUCTION ONLY - NO DEVELOPMENT DEPENDENCIES

# Optional GPU support (for ML models)
# Uncomment if GPU available:
# torch-audio==2.1.2
# torchvision==0.16.2

# File format support
openpyxl==3.1.2
xlsxwriter==3.1.9

# Image processing (if needed for charts)
Pillow==10.1.0
matplotlib==3.8.2
seaborn==0.13.0
plotly==5.17.0

# Performance optimization
cython==3.0.6
numba==0.58.1

# API rate limiting
ratelimit==2.2.1
backoff==2.2.1

# Configuration management
python-decouple==3.8
configobj==5.0.8

# Task scheduling
apscheduler==3.10.4
celery==5.3.4

# Memory optimization
memory-profiler==0.61.0
pympler==0.9

# Additional financial libraries
yfinance==0.2.28
alpha-vantage==2.3.1
ccxt==4.2.1

# Enhanced data processing
polars==0.20.2
pyarrow==14.0.2

# Natural language processing
nltk==3.8.1
spacy==3.7.2
textblob==0.17.1

# Sentiment analysis
vaderSentiment==3.3.2

# Redis for caching (optional)
redis==5.0.1
aioredis==2.0.1

# Advanced plotting and visualization
bokeh==3.3.2
dash==2.16.1

# Financial data providers
investpy==1.0.8
yahoo-fin==*******

# Time zone handling
zoneinfo==0.2.1

# Additional async libraries
aiofiles==23.2.1
aiomultiprocess==0.9.0

# Data serialization
orjson==3.9.10
msgpack==1.0.7

# Enhanced error handling
sentry-sdk==1.39.2

# Database migrations
yoyo-migrations==8.2.0

# Performance profiling
py-spy==0.3.14
line-profiler==4.1.1

# Git operations for code evolution
GitPython==3.1.40

# MCP (Model Context Protocol) for enhanced AI integration
mcp==1.0.0
pydantic-core==2.14.6

# Additional performance optimizations for MCP
uvloop==0.19.0
orjson==3.9.10
