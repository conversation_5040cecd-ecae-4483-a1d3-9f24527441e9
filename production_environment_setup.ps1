# Production Environment Setup for Bybit Trading Bot
# Configures E: drive deployment with production excellence

Write-Host "[PRODUCTION] Configuring Production Environment..." -ForegroundColor Green

# Set production environment variables
$env:ENVIRONMENT = "production"
$env:DEBUG_MODE = "false"
$env:LIVE_TRADING_ONLY = "true"
$env:NO_PAPER_TRADING = "true"
$env:SUPERGPT_ENABLED = "true"
$env:MAXIMUM_PROFIT_MODE = "true"
$env:AGGRESSIVE_TRADING = "true"
$env:ALL_FUNCTIONS_ACTIVE = "true"

# Configure E: drive paths
$env:PYTHONPATH = "E:\The_real_deal_copy\Bybit_Bot\BOT"
$env:CONDA_PREFIX = "E:\conda\envs\bybit-trader"
$env:CONDA_DEFAULT_ENV = "bybit-trader"

# Production data directories
$dataDir = "E:\bybit_bot_data"
$logsDir = "E:\bybit_bot_logs"
$backupDir = "E:\bybit_bot_backups"

# Create production directories
if (!(Test-Path $dataDir)) {
    New-Item -ItemType Directory -Path $dataDir -Force
    Write-Host "[OK] Created data directory: $dataDir" -ForegroundColor Green
}

if (!(Test-Path $logsDir)) {
    New-Item -ItemType Directory -Path $logsDir -Force
    Write-Host "[OK] Created logs directory: $logsDir" -ForegroundColor Green
}

if (!(Test-Path $backupDir)) {
    New-Item -ItemType Directory -Path $backupDir -Force
    Write-Host "[OK] Created backup directory: $backupDir" -ForegroundColor Green
}

# Configure production logging
$env:LOG_LEVEL = "INFO"
$env:LOG_FILE = "$logsDir\trading_bot_production.log"
$env:AUDIT_LOG_FILE = "$logsDir\audit_production.log"

# Security configuration
$env:ENCRYPTION_ENABLED = "true"
$env:AUDIT_LOGGING = "true"
$env:RATE_LIMITING_ENABLED = "true"

# Performance optimization
$env:PERFORMANCE_MODE = "maximum"
$env:CONNECTION_POOL_SIZE = "1000"
$env:MAX_CONNECTIONS_PER_HOST = "200"

# Bybit production configuration
$env:BYBIT_BASE_URL = "https://api.bybit.com"
$env:BYBIT_WS_PUBLIC = "wss://stream.bybit.com/v5/public/spot"
$env:BYBIT_WS_PRIVATE = "wss://stream.bybit.com/v5/private"

Write-Host "[PRODUCTION] Environment configuration completed!" -ForegroundColor Green
Write-Host "[PRODUCTION] Ready for live trading operations" -ForegroundColor Yellow

# Validate environment
Write-Host "[VALIDATION] Checking environment..." -ForegroundColor Cyan
Write-Host "Python Path: $env:PYTHONPATH" -ForegroundColor White
Write-Host "Conda Environment: $env:CONDA_DEFAULT_ENV" -ForegroundColor White
Write-Host "Data Directory: $dataDir" -ForegroundColor White
Write-Host "Logs Directory: $logsDir" -ForegroundColor White
Write-Host "Backup Directory: $backupDir" -ForegroundColor White
Write-Host "[VALIDATION] Environment validation completed!" -ForegroundColor Green
