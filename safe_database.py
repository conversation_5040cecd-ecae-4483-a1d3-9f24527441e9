import sqlite3
import logging
from contextlib import contextmanager
from typing import Any, List, Dict, Tuple

logger = logging.getLogger(__name__)

class SafeDatabaseManager:
    """Safe database manager that prevents sys.warnoptions errors"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self._connection = None
        
    @contextmanager
    def get_connection(self):
        """Get safe database connection"""
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row  # Enable dict-like access
            yield conn
        except Exception as e:
            logger.error(f"Database connection error: {e}")
            yield None
        finally:
            if conn:
                conn.close()
                
    def execute_query(self, query: str, params: Tuple[Any, ...] = ()) -> List[Dict[str, Any]]:
        """Execute query safely"""
        try:
            with self.get_connection() as conn:
                if conn is None:
                    return []
                    
                cursor = conn.cursor()
                cursor.execute(query, params)
                
                # Convert rows to dictionaries
                columns = [description[0] for description in cursor.description] if cursor.description else []
                rows = cursor.fetchall()
                
                result = []
                for row in rows:
                    row_dict = {}
                    for i, column in enumerate(columns):
                        row_dict[column] = row[i] if i < len(row) else None
                    result.append(row_dict)
                    
                return result
                
        except Exception as e:
            logger.error(f"Query execution error: {e}")
            return []
            
    def execute_update(self, query: str, params: Tuple[Any, ...] = ()) -> bool:
        """Execute update/insert safely"""
        try:
            with self.get_connection() as conn:
                if conn is None:
                    return False
                    
                cursor = conn.cursor()
                cursor.execute(query, params)
                conn.commit()
                return True
                
        except Exception as e:
            logger.error(f"Update execution error: {e}")
            return False
            
    def fetch_all(self, table: str, where_clause: str = "", params: Tuple[Any, ...] = ()) -> List[Dict[str, Any]]:
        """Fetch all records safely"""
        query = f"SELECT * FROM {table}"
        if where_clause:
            query += f" WHERE {where_clause}"
            
        return self.execute_query(query, params)

# Create global database manager
_db_manager = None

def get_safe_db_manager(db_path: str = "bybit_trading_bot.db"):
    """Get global safe database manager"""
    global _db_manager
    if _db_manager is None:
        _db_manager = SafeDatabaseManager(db_path)
    return _db_manager

def safe_get_trades(**kwargs) -> List[Dict[str, Any]]:
    """Safely get trades from database"""
    try:
        db_manager = get_safe_db_manager()
        return db_manager.fetch_all("trades")
    except Exception as e:
        logger.error(f"Safe get trades error: {e}")
        return []

def safe_get_async_session():
    """
    This function is deprecated and should not be used.
    It was a workaround for issues with the original get_async_session.
    A proper asynchronous database manager should be used instead.
    """
    logger.error("safe_get_async_session is deprecated and should not be used in production.")
    raise NotImplementedError("safe_get_async_session is not implemented for production use.")
