try:
    from safe_correlation import safe_correlation_calculation
except ImportError:
    def safe_correlation_calculation(a, b): return 0.0

"""
TARGETED sys.warnoptions fix for correlation calculations
Applies minimal changes to resolve pandas/numpy import issues
"""
import sys
import warnings

def apply_sys_warnoptions_fix():
    """Apply targeted sys.warnoptions fix for correlation calculations"""
    try:
        # Add sys.warnoptions attribute if missing
        if not hasattr(sys, 'warnoptions'):
            sys.warnoptions = []
            print("[OK] Added missing sys.warnoptions attribute")
        
        # Configure warnings for pandas/numpy compatibility
        warnings.filterwarnings('ignore', category=FutureWarning)
        warnings.filterwarnings('ignore', category=DeprecationWarning)
        warnings.filterwarnings('ignore', category=UserWarning)
        warnings.filterwarnings('ignore', category=RuntimeWarning)
        
        print("[OK] sys.warnoptions fix applied successfully")
        return True
        
    except Exception as e:
        print(f"[ERROR] Failed to apply sys.warnoptions fix: {e}")
        return False

if __name__ == "__main__":
    apply_sys_warnoptions_fix()
