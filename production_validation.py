#!/usr/bin/env python3
"""
Production Readiness Validation System
Comprehensive testing and validation for live trading deployment
"""

import asyncio
import sys
import os
import time
import json
from datetime import datetime
from typing import Dict, List, Any
import requests
import aiohttp

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class ProductionValidator:
    """Comprehensive production validation system"""
    
    def __init__(self):
        self.validation_results = {}
        self.start_time = time.time()
        self.api_uptime_checks = 0
        self.api_successful_checks = 0
        
    async def run_comprehensive_validation(self) -> Dict[str, Any]:
        """Run complete production validation suite"""
        print("=" * 80)
        print("PRODUCTION READINESS VALIDATION")
        print("=" * 80)
        print(f"Validation started: {datetime.now().isoformat()}")
        print()
        
        validation_tasks = [
            ("Environment Configuration", self._validate_environment),
            ("API Connectivity", self._validate_api_connectivity),
            ("System Components", self._validate_system_components),
            ("Security Configuration", self._validate_security),
            ("Performance Metrics", self._validate_performance),
            ("Data Pipeline", self._validate_data_pipeline),
            ("Risk Management", self._validate_risk_management),
            ("Trading Execution", self._validate_trading_execution),
            ("Mock Data Elimination", self._validate_no_mock_data),
            ("Production Readiness", self._validate_production_readiness)
        ]
        
        for task_name, task_func in validation_tasks:
            print(f"[VALIDATING] {task_name}...")
            try:
                result = await task_func()
                self.validation_results[task_name] = result
                status = "PASS" if result.get('passed', False) else "FAIL"
                print(f"[{status}] {task_name}: {result.get('message', 'No message')}")
            except Exception as e:
                self.validation_results[task_name] = {
                    'passed': False,
                    'message': f"Validation error: {e}",
                    'error': str(e)
                }
                print(f"[ERROR] {task_name}: {e}")
            print()
        
        # Generate final report
        return self._generate_validation_report()
    
    async def _validate_environment(self) -> Dict[str, Any]:
        """Validate production environment configuration"""
        try:
            checks = []
            
            # Check E: drive setup
            e_drive_exists = os.path.exists("E:/")
            checks.append(("E: drive accessible", e_drive_exists))
            
            # Check conda environment
            conda_env = os.getenv('CONDA_DEFAULT_ENV')
            checks.append(("Conda environment", conda_env == 'bybit-trader'))
            
            # Check production environment variables
            prod_vars = [
                'LIVE_TRADING_ONLY',
                'NO_PAPER_TRADING',
                'MAXIMUM_PROFIT_MODE'
            ]
            for var in prod_vars:
                checks.append((f"Env var {var}", os.getenv(var) == 'true'))
            
            # Check data directories
            data_dirs = [
                "E:/bybit_bot_data",
                "E:/bybit_bot_logs",
                "E:/bybit_bot_backups"
            ]
            for dir_path in data_dirs:
                checks.append((f"Directory {dir_path}", os.path.exists(dir_path)))
            
            passed = all(check[1] for check in checks)
            return {
                'passed': passed,
                'message': f"Environment validation: {len([c for c in checks if c[1]])}/{len(checks)} checks passed",
                'details': checks
            }
            
        except Exception as e:
            return {'passed': False, 'message': f"Environment validation failed: {e}"}
    
    async def _validate_api_connectivity(self) -> Dict[str, Any]:
        """Validate Bybit API connectivity and latency"""
        try:
            # Test API connectivity with multiple requests
            latencies = []
            success_count = 0
            
            for i in range(10):
                start_time = time.perf_counter()
                try:
                    response = requests.get('https://api.bybit.com/v5/market/time', timeout=5)
                    end_time = time.perf_counter()
                    
                    if response.status_code == 200:
                        latency = (end_time - start_time) * 1000
                        latencies.append(latency)
                        success_count += 1
                        self.api_successful_checks += 1
                    
                    self.api_uptime_checks += 1
                    
                except Exception as e:
                    self.api_uptime_checks += 1
                    continue
            
            if latencies:
                avg_latency = sum(latencies) / len(latencies)
                min_latency = min(latencies)
                max_latency = max(latencies)
                uptime_percentage = (success_count / 10) * 100
                
                # Check if meets production requirements
                latency_ok = avg_latency < 500  # Allow up to 500ms average
                uptime_ok = uptime_percentage >= 90  # Require 90% uptime
                
                return {
                    'passed': latency_ok and uptime_ok,
                    'message': f"API connectivity: {avg_latency:.1f}ms avg latency, {uptime_percentage:.1f}% uptime",
                    'details': {
                        'average_latency': avg_latency,
                        'min_latency': min_latency,
                        'max_latency': max_latency,
                        'uptime_percentage': uptime_percentage,
                        'successful_requests': success_count
                    }
                }
            else:
                return {
                    'passed': False,
                    'message': "API connectivity: No successful connections",
                    'details': {'successful_requests': 0}
                }
                
        except Exception as e:
            return {'passed': False, 'message': f"API validation failed: {e}"}
    
    async def _validate_system_components(self) -> Dict[str, Any]:
        """Validate core system components"""
        try:
            components = [
                'main_unified_system',
                'bybit_bot.core.config',
                'bybit_bot.exchange.enhanced_bybit_client',
                'bybit_bot.risk.advanced_risk_manager',
                'bybit_bot.agents.trading_agent'
            ]
            
            import_results = []
            for component in components:
                try:
                    __import__(component)
                    import_results.append((component, True))
                except Exception as e:
                    import_results.append((component, False))
            
            passed = all(result[1] for result in import_results)
            return {
                'passed': passed,
                'message': f"Component validation: {len([r for r in import_results if r[1]])}/{len(import_results)} components imported",
                'details': import_results
            }
            
        except Exception as e:
            return {'passed': False, 'message': f"Component validation failed: {e}"}
    
    async def _validate_security(self) -> Dict[str, Any]:
        """Validate security configuration"""
        try:
            security_checks = []
            
            # Check credential management
            cred_file_exists = os.path.exists("secure_credential_manager.py")
            security_checks.append(("Credential manager", cred_file_exists))
            
            # Check encryption settings
            encryption_enabled = os.getenv('ENCRYPTION_ENABLED') == 'true'
            security_checks.append(("Encryption enabled", encryption_enabled))
            
            # Check audit logging
            audit_enabled = os.getenv('AUDIT_LOGGING') == 'true'
            security_checks.append(("Audit logging", audit_enabled))
            
            passed = all(check[1] for check in security_checks)
            return {
                'passed': passed,
                'message': f"Security validation: {len([c for c in security_checks if c[1]])}/{len(security_checks)} checks passed",
                'details': security_checks
            }
            
        except Exception as e:
            return {'passed': False, 'message': f"Security validation failed: {e}"}
    
    async def _validate_performance(self) -> Dict[str, Any]:
        """Validate performance configuration"""
        try:
            # Check performance settings
            perf_mode = os.getenv('PERFORMANCE_MODE')
            connection_pool = os.getenv('CONNECTION_POOL_SIZE')
            
            performance_ok = (
                perf_mode == 'maximum' and
                connection_pool and int(connection_pool) >= 1000
            )
            
            return {
                'passed': performance_ok,
                'message': f"Performance validation: {'PASS' if performance_ok else 'FAIL'}",
                'details': {
                    'performance_mode': perf_mode,
                    'connection_pool_size': connection_pool
                }
            }
            
        except Exception as e:
            return {'passed': False, 'message': f"Performance validation failed: {e}"}
    
    async def _validate_data_pipeline(self) -> Dict[str, Any]:
        """Validate data pipeline configuration"""
        try:
            # Check for removed mock data files
            mock_files_removed = not any([
                os.path.exists("paper_trading_system.py"),
                os.path.exists("paper_trading_log.txt"),
                os.path.exists("tests/")
            ])
            
            return {
                'passed': mock_files_removed,
                'message': f"Data pipeline validation: {'Mock data eliminated' if mock_files_removed else 'Mock data still present'}",
                'details': {'mock_data_eliminated': mock_files_removed}
            }
            
        except Exception as e:
            return {'passed': False, 'message': f"Data pipeline validation failed: {e}"}
    
    async def _validate_risk_management(self) -> Dict[str, Any]:
        """Validate risk management system"""
        try:
            # Check risk management files
            risk_files = [
                "bybit_bot/risk/advanced_risk_manager.py",
                "bybit_bot/agents/risk_agent.py"
            ]
            
            risk_files_exist = all(os.path.exists(f) for f in risk_files)
            
            return {
                'passed': risk_files_exist,
                'message': f"Risk management validation: {'PASS' if risk_files_exist else 'FAIL'}",
                'details': {'risk_files_present': risk_files_exist}
            }
            
        except Exception as e:
            return {'passed': False, 'message': f"Risk management validation failed: {e}"}
    
    async def _validate_trading_execution(self) -> Dict[str, Any]:
        """Validate trading execution system"""
        try:
            # Check trading components
            trading_files = [
                "bybit_bot/agents/trading_agent.py",
                "bybit_bot/exchange/enhanced_bybit_client.py"
            ]
            
            trading_files_exist = all(os.path.exists(f) for f in trading_files)
            
            return {
                'passed': trading_files_exist,
                'message': f"Trading execution validation: {'PASS' if trading_files_exist else 'FAIL'}",
                'details': {'trading_files_present': trading_files_exist}
            }
            
        except Exception as e:
            return {'passed': False, 'message': f"Trading execution validation failed: {e}"}
    
    async def _validate_no_mock_data(self) -> Dict[str, Any]:
        """Validate elimination of mock data"""
        try:
            # Check for removed test/mock files
            prohibited_files = [
                "debug_",
                "test_",
                "mock_",
                "paper_trading",
                "simulation",
                "demo_"
            ]
            
            found_prohibited = []
            for root, dirs, files in os.walk("."):
                for file in files:
                    if any(prohibited in file.lower() for prohibited in prohibited_files):
                        found_prohibited.append(os.path.join(root, file))
            
            # Filter out allowed files
            allowed_exceptions = [
                "api_latency_test.py",  # Production testing
                "production_validation.py",  # This file
                "secure_credential_manager.py"  # Production security
            ]
            
            actual_violations = [f for f in found_prohibited if not any(allowed in f for allowed in allowed_exceptions)]
            
            return {
                'passed': len(actual_violations) == 0,
                'message': f"Mock data elimination: {len(actual_violations)} violations found",
                'details': {'violations': actual_violations}
            }
            
        except Exception as e:
            return {'passed': False, 'message': f"Mock data validation failed: {e}"}
    
    async def _validate_production_readiness(self) -> Dict[str, Any]:
        """Final production readiness check"""
        try:
            # Calculate overall score
            passed_validations = sum(1 for result in self.validation_results.values() if result.get('passed', False))
            total_validations = len(self.validation_results)
            
            if total_validations == 0:
                return {'passed': False, 'message': "No validations completed"}
            
            success_rate = (passed_validations / total_validations) * 100
            production_ready = success_rate >= 90  # Require 90% pass rate
            
            return {
                'passed': production_ready,
                'message': f"Production readiness: {success_rate:.1f}% ({passed_validations}/{total_validations} validations passed)",
                'details': {
                    'success_rate': success_rate,
                    'passed_validations': passed_validations,
                    'total_validations': total_validations,
                    'production_ready': production_ready
                }
            }
            
        except Exception as e:
            return {'passed': False, 'message': f"Production readiness check failed: {e}"}
    
    def _generate_validation_report(self) -> Dict[str, Any]:
        """Generate comprehensive validation report"""
        total_time = time.time() - self.start_time
        
        # Calculate API uptime
        api_uptime = (self.api_successful_checks / max(self.api_uptime_checks, 1)) * 100
        
        # Overall status
        passed_count = sum(1 for result in self.validation_results.values() if result.get('passed', False))
        total_count = len(self.validation_results)
        overall_success = (passed_count / max(total_count, 1)) * 100
        
        production_ready = overall_success >= 90 and api_uptime >= 99.0
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'validation_duration': f"{total_time:.2f} seconds",
            'overall_success_rate': f"{overall_success:.1f}%",
            'api_uptime': f"{api_uptime:.1f}%",
            'production_ready': production_ready,
            'passed_validations': passed_count,
            'total_validations': total_count,
            'detailed_results': self.validation_results,
            'summary': {
                'status': 'PRODUCTION READY' if production_ready else 'NOT READY',
                'critical_issues': [
                    name for name, result in self.validation_results.items()
                    if not result.get('passed', False)
                ]
            }
        }
        
        return report

async def main():
    """Main validation execution"""
    validator = ProductionValidator()
    report = await validator.run_comprehensive_validation()
    
    # Print final report
    print("=" * 80)
    print("FINAL VALIDATION REPORT")
    print("=" * 80)
    print(f"Overall Status: {report['summary']['status']}")
    print(f"Success Rate: {report['overall_success_rate']}")
    print(f"API Uptime: {report['api_uptime']}")
    print(f"Validation Duration: {report['validation_duration']}")
    
    if report['summary']['critical_issues']:
        print("\nCritical Issues:")
        for issue in report['summary']['critical_issues']:
            print(f"  - {issue}")
    
    # Save report
    report_file = f"E:/bybit_bot_logs/validation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    os.makedirs(os.path.dirname(report_file), exist_ok=True)
    with open(report_file, 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"\nDetailed report saved to: {report_file}")
    print("=" * 80)
    
    return report['production_ready']

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
