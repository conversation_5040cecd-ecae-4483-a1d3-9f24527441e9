#!/usr/bin/env python3
"""
Production API Connectivity Testing
Tests live Bybit V5 API connections, measures response times, validates uptime
"""

import asyncio
import time
import statistics
from datetime import datetime, timedelta
from typing import Dict, Any
import aiohttp
import json

# Credential manager import removed for standalone testing

class APIConnectivityTester:
    """Test Bybit V5 API connectivity and performance"""
    
    def __init__(self):
        self.base_url = "https://api.bybit.com"
        self.test_results = []
        self.start_time = None
        self.api_key = None
        self.api_secret = None
        
    async def initialize(self):
        """Initialize with secure credentials"""
        try:
            # For this test, we'll focus on public endpoints
            # Credentials would be loaded here for authenticated tests
            print("[INFO] Initializing API connectivity tester")
            print("[INFO] Testing public endpoints (no credentials required)")
            return True
        except Exception as e:
            print(f"[ERROR] Failed to initialize: {e}")
            return False
    
    async def test_public_endpoints(self) -> Dict[str, Any]:
        """Test public API endpoints"""
        print("\n[TEST] Testing public API endpoints...")
        
        public_tests = [
            {
                "name": "Server Time",
                "endpoint": "/v5/market/time",
                "method": "GET"
            },
            {
                "name": "Market Instruments",
                "endpoint": "/v5/market/instruments-info?category=linear&symbol=BTCUSDT",
                "method": "GET"
            },
            {
                "name": "Market Ticker",
                "endpoint": "/v5/market/tickers?category=linear&symbol=BTCUSDT",
                "method": "GET"
            },
            {
                "name": "Order Book",
                "endpoint": "/v5/market/orderbook?category=linear&symbol=BTCUSDT&limit=25",
                "method": "GET"
            },
            {
                "name": "Recent Trades",
                "endpoint": "/v5/market/recent-trade?category=linear&symbol=BTCUSDT&limit=10",
                "method": "GET"
            }
        ]
        
        results = []
        
        async with aiohttp.ClientSession() as session:
            for test in public_tests:
                start_time = time.time()
                try:
                    url = f"{self.base_url}{test['endpoint']}"
                    async with session.get(url) as response:
                        response_time = (time.time() - start_time) * 1000  # ms
                        
                        if response.status == 200:
                            data = await response.json()
                            success = data.get('retCode') == 0
                            
                            result = {
                                "test": test["name"],
                                "status": "SUCCESS" if success else "FAILED",
                                "response_time_ms": round(response_time, 2),
                                "http_status": response.status,
                                "api_status": data.get('retCode', 'unknown')
                            }
                            
                            print(f"  ✅ {test['name']}: {response_time:.2f}ms")
                        else:
                            result = {
                                "test": test["name"],
                                "status": "FAILED",
                                "response_time_ms": round(response_time, 2),
                                "http_status": response.status,
                                "error": f"HTTP {response.status}"
                            }
                            print(f"  ❌ {test['name']}: HTTP {response.status}")
                            
                except Exception as e:
                    result = {
                        "test": test["name"],
                        "status": "ERROR",
                        "response_time_ms": round((time.time() - start_time) * 1000, 2),
                        "error": str(e)
                    }
                    print(f"  ❌ {test['name']}: {e}")
                
                results.append(result)
                await asyncio.sleep(0.1)  # Rate limiting
        
        return {
            "public_endpoints": results,
            "success_rate": len([r for r in results if r["status"] == "SUCCESS"]) / len(results) * 100
        }
    
    async def test_authenticated_endpoints(self) -> Dict[str, Any]:
        """Test authenticated API endpoints"""
        print("\n[TEST] Authenticated endpoints test...")
        print("  🔒 Skipping authenticated tests for this connectivity validation")
        print("  🔒 Focus: Public API endpoints and connectivity performance")

        return {
            "authenticated_endpoints": "skipped_for_connectivity_test",
            "note": "Authenticated endpoints skipped - focusing on public API connectivity"
        }
    
    async def measure_latency_over_time(self, duration_minutes: int = 5) -> Dict[str, Any]:
        """Measure API latency over a specified time period"""
        print(f"\n[TEST] Measuring API latency over {duration_minutes} minutes...")
        
        end_time = datetime.now() + timedelta(minutes=duration_minutes)
        latencies = []
        successful_requests = 0
        failed_requests = 0
        
        async with aiohttp.ClientSession() as session:
            while datetime.now() < end_time:
                start_time = time.time()
                try:
                    url = f"{self.base_url}/v5/market/time"
                    async with session.get(url) as response:
                        response_time = (time.time() - start_time) * 1000
                        
                        if response.status == 200:
                            data = await response.json()
                            if data.get('retCode') == 0:
                                latencies.append(response_time)
                                successful_requests += 1
                            else:
                                failed_requests += 1
                        else:
                            failed_requests += 1
                            
                except Exception as e:
                    failed_requests += 1
                
                await asyncio.sleep(10)  # Test every 10 seconds
        
        if latencies:
            return {
                "duration_minutes": duration_minutes,
                "total_requests": successful_requests + failed_requests,
                "successful_requests": successful_requests,
                "failed_requests": failed_requests,
                "success_rate_percent": (successful_requests / (successful_requests + failed_requests)) * 100,
                "latency_stats": {
                    "min_ms": round(min(latencies), 2),
                    "max_ms": round(max(latencies), 2),
                    "avg_ms": round(statistics.mean(latencies), 2),
                    "median_ms": round(statistics.median(latencies), 2)
                }
            }
        else:
            return {
                "error": "No successful requests during test period",
                "failed_requests": failed_requests
            }
    
    async def run_comprehensive_test(self) -> Dict[str, Any]:
        """Run comprehensive API connectivity tests"""
        print("🚀 STARTING COMPREHENSIVE API CONNECTIVITY TESTING")
        print("=" * 60)
        
        self.start_time = datetime.now()
        
        # Initialize credentials
        if not await self.initialize():
            return {"error": "Failed to initialize credentials"}
        
        # Run all tests
        results = {
            "test_start_time": self.start_time.isoformat(),
            "bybit_api_base_url": self.base_url
        }
        
        # Test public endpoints
        results.update(await self.test_public_endpoints())
        
        # Test authenticated endpoints
        results.update(await self.test_authenticated_endpoints())
        
        # Measure latency over time (shorter duration for testing)
        results.update(await self.measure_latency_over_time(duration_minutes=2))
        
        results["test_end_time"] = datetime.now().isoformat()
        results["total_test_duration"] = str(datetime.now() - self.start_time)
        
        return results

async def main():
    """Main test execution"""
    tester = APIConnectivityTester()
    results = await tester.run_comprehensive_test()
    
    print("\n" + "=" * 60)
    print("📊 API CONNECTIVITY TEST RESULTS")
    print("=" * 60)
    
    # Print summary
    if "public_endpoints" in results:
        public_success = results.get("success_rate", 0)
        print(f"Public Endpoints Success Rate: {public_success:.1f}%")
    
    if "success_rate_percent" in results:
        uptime = results.get("success_rate_percent", 0)
        print(f"API Uptime During Test: {uptime:.1f}%")
    
    if "latency_stats" in results:
        latency = results["latency_stats"]
        print(f"Average Response Time: {latency['avg_ms']}ms")
        print(f"Response Time Range: {latency['min_ms']}ms - {latency['max_ms']}ms")
    
    # Determine overall status
    overall_success = True
    if "success_rate" in results and results["success_rate"] < 90:
        overall_success = False
    if "success_rate_percent" in results and results["success_rate_percent"] < 95:
        overall_success = False
    
    print(f"\n🎯 OVERALL STATUS: {'SUCCESS' if overall_success else 'NEEDS ATTENTION'}")
    
    # Save detailed results
    with open("api_connectivity_results.json", "w") as f:
        json.dump(results, f, indent=2)
    
    print(f"📄 Detailed results saved to: api_connectivity_results.json")
    
    return overall_success

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
